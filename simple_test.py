"""
简化的测试程序 - 用于调试界面显示问题
"""
import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import Qt


class SimpleTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("简单测试窗口")
        self.setGeometry(200, 200, 600, 400)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        
        # 添加标签
        label = QLabel("如果您能看到这个窗口，说明PyQt5基本功能正常！")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 16px; padding: 20px;")
        layout.addWidget(label)
        
        # 添加系统信息
        info_label = QLabel(f"Python版本: {sys.version}\n操作系统: {os.name}")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # 添加按钮
        button = QPushButton("点击测试")
        button.clicked.connect(self.on_button_click)
        button.setStyleSheet("font-size: 14px; padding: 10px;")
        layout.addWidget(button)
        
        central_widget.setLayout(layout)
        
        # 强制显示窗口
        self.show()
        self.raise_()
        self.activateWindow()
        
        # 确保窗口在屏幕中央
        self.center_window()
        
        print("简单测试窗口已创建")
    
    def center_window(self):
        """将窗口居中显示"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def on_button_click(self):
        print("按钮被点击了！界面响应正常。")
        self.sender().setText("界面正常工作！")


def main():
    print("启动简化测试程序...")
    print("如果看不到窗口，可能是以下原因：")
    print("1. 显示器配置问题")
    print("2. PyQt5安装问题") 
    print("3. 系统权限问题")
    print("4. 窗口被其他程序遮挡")
    
    # 设置环境变量（可能有助于解决显示问题）
    os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
    
    try:
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setQuitOnLastWindowClosed(True)
        
        print("创建测试窗口...")
        window = SimpleTestWindow()
        
        print("显示窗口...")
        window.show()
        window.raise_()
        window.activateWindow()
        
        # 强制处理事件
        app.processEvents()
        
        print("窗口应该已经显示。如果没有看到，请检查任务栏或按Alt+Tab切换窗口。")
        print("程序正在运行，按Ctrl+C退出...")
        
        # 运行事件循环
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        input("按Enter键退出...")


if __name__ == "__main__":
    main()
