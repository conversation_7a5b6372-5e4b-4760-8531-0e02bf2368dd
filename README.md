# Schelling隔离模型仿真

这是一个基于PyQt5的Schelling隔离模型仿真程序，支持GPU加速计算，用于研究社会隔离现象的形成机制。

## 🚀 新特性 - GPU加速版本

- **GPU加速计算**: 使用CUDA和CuPy实现大规模网格的高速仿真
- **性能监控**: 实时显示FPS、计算时间和GPU使用状态
- **智能回退**: 自动检测GPU可用性，无GPU时回退到CPU模式
- **优化渲染**: 改进的可视化引擎，支持更大规模的仿真
- **性能对比**: 内置CPU vs GPU性能测试工具

## 模型简介

Schelling隔离模型是由诺贝尔经济学奖得主托马斯·谢林（Thomas Schelling）在1971年提出的经典社会科学模型。该模型展示了即使个体只有轻微的同质性偏好，也可能导致严重的社会隔离现象。

## 功能特性

### 核心功能
- **可视化界面**: 实时显示代理在网格中的分布和移动
- **参数配置**: 可调整网格大小（最大200x200）、人口密度、群体比例和相似性阈值
- **仿真控制**: 支持开始、暂停、重置和速度调节（1-20级）
- **统计分析**: 实时显示满意度、隔离指数等关键指标
- **群体分析**: 分别统计不同群体的满意度情况

### GPU加速功能
- **CUDA加速**: 使用CuPy进行并行计算，大幅提升大规模仿真性能
- **性能监控**: 实时显示FPS、步骤时间、GPU状态
- **动态切换**: 运行时可在CPU/GPU模式间切换
- **内存优化**: 高效的GPU内存管理和数据传输
- **自动检测**: 智能检测CUDA环境，无GPU时自动使用CPU

## 安装和运行

### 环境要求

- Python 3.7+
- PyQt5
- NumPy
- CuPy (可选，用于GPU加速)
- CUDA Toolkit (可选，用于GPU加速)

### 安装依赖

#### 基础安装 (仅CPU版本)
```bash
pip install PyQt5 numpy
```

#### GPU加速安装
```bash
# 基础依赖
pip install PyQt5 numpy

# GPU加速依赖 (根据您的CUDA版本选择)
pip install cupy-cuda11x  # CUDA 11.x
# 或
pip install cupy-cuda12x  # CUDA 12.x
```

#### 使用requirements.txt
```bash
pip install -r requirements.txt
# 然后根据您的CUDA版本安装CuPy
```

### 运行程序

#### 标准版本 (CPU)
```bash
python main.py
```

#### GPU加速版本
```bash
python main_gpu.py
```

#### 性能测试
```bash
# 完整性能对比测试
python performance_test.py

# 快速测试
python performance_test.py quick
```

## 使用说明

### 参数配置

1. **网格大小**: 设置仿真环境的宽度和高度
2. **人口密度**: 控制网格中被占用位置的比例（0.1-1.0）
3. **少数群体比例**: 设置少数群体在总人口中的比例（0.1-0.9）
4. **相似性阈值**: 代理满意所需的同类邻居比例（0.0-1.0）

### 仿真控制

- **开始**: 启动仿真，代理开始根据满意度进行移动
- **暂停**: 暂停仿真，可以观察当前状态
- **重置**: 重新初始化模型，生成新的随机分布
- **速度控制**: 调节仿真的更新频率（1-10级）

### 可视化说明

- **蓝色**: 少数群体代理
  - 深蓝色: 满意的代理
  - 浅蓝色: 不满意的代理
- **红色**: 多数群体代理
  - 深红色: 满意的代理
  - 浅红色: 不满意的代理
- **白色**: 空位置

### 统计指标

- **迭代次数**: 仿真进行的步数
- **总体满意度**: 所有代理中满意的比例
- **隔离指数**: 衡量群体隔离程度的指标（0-1，越高越隔离）
- **群体统计**: 各群体的数量和满意度

## 模型原理

### 核心机制

1. **初始化**: 在网格中随机分布两种类型的代理
2. **满意度计算**: 每个代理检查其邻居中同类型代理的比例
3. **移动决策**: 不满意的代理随机移动到空位置
4. **迭代更新**: 重复计算满意度和移动，直到达到平衡

### 关键发现

- 即使个体只有轻微的同质性偏好（如30%），也可能导致高度的空间隔离
- 隔离程度往往超过个体的实际偏好水平
- 这说明了微观动机与宏观结果之间的复杂关系

## 实验建议

### 基础实验

1. **阈值效应**: 观察不同相似性阈值对最终隔离程度的影响
2. **密度影响**: 比较不同人口密度下的隔离模式
3. **群体比例**: 研究少数群体比例对隔离结果的影响

### 高级实验

1. **动态分析**: 观察隔离指数随时间的变化趋势
2. **参数敏感性**: 系统性地测试各参数对结果的影响
3. **平衡态分析**: 研究不同参数组合下的稳定状态

## 文件结构

```
schelling_segregation_model/
├── main.py                 # 标准版主程序入口
├── main_gpu.py            # GPU加速版主程序入口
├── schelling_model.py     # 标准版模型核心逻辑
├── schelling_model_gpu.py # GPU加速版模型逻辑
├── gui.py                 # 标准版PyQt5用户界面
├── gui_gpu.py            # GPU加速版用户界面
├── performance_test.py    # 性能测试和对比工具
├── test_model.py         # 功能测试脚本
├── test_gui.py           # GUI测试脚本
├── requirements.txt      # 依赖包列表
└── README.md            # 项目说明文档
```

## GPU加速原理

### 计算优化
- **并行邻居计算**: 使用CUDA并行计算所有代理的邻居统计
- **向量化操作**: 利用GPU的SIMD架构进行批量满意度计算
- **内存合并访问**: 优化内存访问模式，提高带宽利用率
- **异步计算**: CPU-GPU异步数据传输，减少等待时间

### 性能提升
- **小规模网格 (50x50)**: 通常CPU更快，GPU有启动开销
- **中等规模网格 (100x100)**: GPU开始显示优势，2-5倍加速
- **大规模网格 (150x150+)**: GPU显著优势，5-20倍加速
- **超大规模网格 (200x200)**: GPU可达到30倍以上加速

## 技术实现

### 标准版本
- **模型层**: 使用面向对象设计，清晰分离代理、网格和模型逻辑
- **界面层**: 基于PyQt5构建响应式用户界面
- **可视化**: 自定义绘制组件，实时渲染网格状态
- **控制层**: 使用定时器机制实现平滑的仿真动画

### GPU加速版本
- **计算后端**: CuPy提供NumPy兼容的CUDA接口
- **内存管理**: 智能的CPU-GPU数据传输和缓存
- **渲染优化**: 图像缓存和增量更新减少重绘开销
- **性能监控**: 实时FPS和计算时间统计

## 扩展建议

1. **数据导出**: 添加统计数据的CSV导出功能
2. **参数预设**: 提供经典实验的参数预设
3. **批量实验**: 支持多次运行并统计平均结果
4. **网络拓扑**: 扩展到其他邻域结构（如六边形网格）
5. **多群体**: 支持两个以上的群体类型

## 参考文献

- Schelling, T. C. (1971). Dynamic models of segregation. Journal of Mathematical Sociology, 1(2), 143-186.
- Schelling, T. C. (1978). Micromotives and Macrobehavior. Norton.

## 许可证

本项目仅供学习和研究使用。
