# Schelling隔离模型仿真

这是一个基于PyQt5的Schelling隔离模型仿真程序，用于研究社会隔离现象的形成机制。

## 模型简介

Schelling隔离模型是由诺贝尔经济学奖得主托马斯·谢林（Thomas Schelling）在1971年提出的经典社会科学模型。该模型展示了即使个体只有轻微的同质性偏好，也可能导致严重的社会隔离现象。

## 功能特性

- **可视化界面**: 实时显示代理在网格中的分布和移动
- **参数配置**: 可调整网格大小、人口密度、群体比例和相似性阈值
- **仿真控制**: 支持开始、暂停、重置和速度调节
- **统计分析**: 实时显示满意度、隔离指数等关键指标
- **群体分析**: 分别统计不同群体的满意度情况

## 安装和运行

### 环境要求

- Python 3.7+
- PyQt5
- NumPy

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```bash
python main.py
```

## 使用说明

### 参数配置

1. **网格大小**: 设置仿真环境的宽度和高度
2. **人口密度**: 控制网格中被占用位置的比例（0.1-1.0）
3. **少数群体比例**: 设置少数群体在总人口中的比例（0.1-0.9）
4. **相似性阈值**: 代理满意所需的同类邻居比例（0.0-1.0）

### 仿真控制

- **开始**: 启动仿真，代理开始根据满意度进行移动
- **暂停**: 暂停仿真，可以观察当前状态
- **重置**: 重新初始化模型，生成新的随机分布
- **速度控制**: 调节仿真的更新频率（1-10级）

### 可视化说明

- **蓝色**: 少数群体代理
  - 深蓝色: 满意的代理
  - 浅蓝色: 不满意的代理
- **红色**: 多数群体代理
  - 深红色: 满意的代理
  - 浅红色: 不满意的代理
- **白色**: 空位置

### 统计指标

- **迭代次数**: 仿真进行的步数
- **总体满意度**: 所有代理中满意的比例
- **隔离指数**: 衡量群体隔离程度的指标（0-1，越高越隔离）
- **群体统计**: 各群体的数量和满意度

## 模型原理

### 核心机制

1. **初始化**: 在网格中随机分布两种类型的代理
2. **满意度计算**: 每个代理检查其邻居中同类型代理的比例
3. **移动决策**: 不满意的代理随机移动到空位置
4. **迭代更新**: 重复计算满意度和移动，直到达到平衡

### 关键发现

- 即使个体只有轻微的同质性偏好（如30%），也可能导致高度的空间隔离
- 隔离程度往往超过个体的实际偏好水平
- 这说明了微观动机与宏观结果之间的复杂关系

## 实验建议

### 基础实验

1. **阈值效应**: 观察不同相似性阈值对最终隔离程度的影响
2. **密度影响**: 比较不同人口密度下的隔离模式
3. **群体比例**: 研究少数群体比例对隔离结果的影响

### 高级实验

1. **动态分析**: 观察隔离指数随时间的变化趋势
2. **参数敏感性**: 系统性地测试各参数对结果的影响
3. **平衡态分析**: 研究不同参数组合下的稳定状态

## 文件结构

```
schelling_segregation_model/
├── main.py              # 主程序入口
├── schelling_model.py   # 模型核心逻辑
├── gui.py              # PyQt5用户界面
├── requirements.txt    # 依赖包列表
└── README.md          # 项目说明文档
```

## 技术实现

- **模型层**: 使用面向对象设计，清晰分离代理、网格和模型逻辑
- **界面层**: 基于PyQt5构建响应式用户界面
- **可视化**: 自定义绘制组件，实时渲染网格状态
- **控制层**: 使用定时器机制实现平滑的仿真动画

## 扩展建议

1. **数据导出**: 添加统计数据的CSV导出功能
2. **参数预设**: 提供经典实验的参数预设
3. **批量实验**: 支持多次运行并统计平均结果
4. **网络拓扑**: 扩展到其他邻域结构（如六边形网格）
5. **多群体**: 支持两个以上的群体类型

## 参考文献

- Schelling, T. C. (1971). Dynamic models of segregation. Journal of Mathematical Sociology, 1(2), 143-186.
- Schelling, T. C. (1978). Micromotives and Macrobehavior. Norton.

## 许可证

本项目仅供学习和研究使用。
