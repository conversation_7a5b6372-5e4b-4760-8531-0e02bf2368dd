"""
测试Schelling模型的核心功能
"""
from schelling_model import SchellingModel, AgentType


def test_model_initialization():
    """测试模型初始化"""
    print("测试模型初始化...")
    model = SchellingModel(width=20, height=20, density=0.8, minority_pc=0.3, homophily=0.3)
    
    print(f"网格大小: {model.width} x {model.height}")
    print(f"总代理数: {model.total_agents}")
    print(f"空位置数: {len(model.empty_cells)}")
    
    # 验证密度
    expected_agents = int(20 * 20 * 0.8)
    assert abs(model.total_agents - expected_agents) <= 1, "代理数量不符合预期"
    
    # 验证空位置数
    expected_empty = 20 * 20 - model.total_agents
    assert len(model.empty_cells) == expected_empty, "空位置数量不符合预期"
    
    print("✓ 模型初始化测试通过")


def test_statistics():
    """测试统计功能"""
    print("\n测试统计功能...")
    model = SchellingModel(width=10, height=10, density=0.5, minority_pc=0.4, homophily=0.5)
    
    stats = model.get_statistics()
    
    print(f"迭代次数: {stats['iteration']}")
    print(f"总体满意度: {stats['satisfaction_rate']:.2%}")
    print(f"隔离指数: {stats['segregation_index']:.3f}")
    print(f"少数群体数量: {stats['type_a_count']}")
    print(f"多数群体数量: {stats['type_b_count']}")
    print(f"少数群体满意度: {stats['type_a_satisfaction']:.2%}")
    print(f"多数群体满意度: {stats['type_b_satisfaction']:.2%}")
    
    # 验证统计数据的一致性
    assert stats['type_a_count'] + stats['type_b_count'] == stats['total_agents'], "群体数量统计错误"
    assert 0 <= stats['satisfaction_rate'] <= 1, "满意度超出范围"
    assert 0 <= stats['segregation_index'] <= 1, "隔离指数超出范围"
    
    print("✓ 统计功能测试通过")


def test_simulation_step():
    """测试仿真步骤"""
    print("\n测试仿真步骤...")
    model = SchellingModel(width=15, height=15, density=0.7, minority_pc=0.3, homophily=0.4)
    
    initial_stats = model.get_statistics()
    print(f"初始满意度: {initial_stats['satisfaction_rate']:.2%}")
    print(f"初始隔离指数: {initial_stats['segregation_index']:.3f}")
    
    # 运行几步仿真
    steps = 0
    max_steps = 50
    
    while steps < max_steps:
        if not model.step():
            print(f"仿真在第{steps}步达到平衡")
            break
        steps += 1
        
        if steps % 10 == 0:
            stats = model.get_statistics()
            print(f"第{steps}步 - 满意度: {stats['satisfaction_rate']:.2%}, 隔离指数: {stats['segregation_index']:.3f}")
    
    final_stats = model.get_statistics()
    print(f"最终满意度: {final_stats['satisfaction_rate']:.2%}")
    print(f"最终隔离指数: {final_stats['segregation_index']:.3f}")
    
    # 验证仿真逻辑
    assert final_stats['iteration'] >= initial_stats['iteration'], "迭代次数应该增加"
    
    print("✓ 仿真步骤测试通过")


def test_reset_functionality():
    """测试重置功能"""
    print("\n测试重置功能...")
    model = SchellingModel(width=12, height=12, density=0.6, minority_pc=0.35, homophily=0.25)
    
    # 运行一些步骤
    for _ in range(10):
        if not model.step():
            break
    
    stats_before_reset = model.get_statistics()
    print(f"重置前迭代次数: {stats_before_reset['iteration']}")
    
    # 重置模型
    model.reset()
    
    stats_after_reset = model.get_statistics()
    print(f"重置后迭代次数: {stats_after_reset['iteration']}")
    
    # 验证重置功能
    assert stats_after_reset['iteration'] == 0, "重置后迭代次数应为0"
    assert stats_after_reset['total_agents'] > 0, "重置后应有代理"
    
    print("✓ 重置功能测试通过")


def main():
    """运行所有测试"""
    print("开始测试Schelling模型...")
    print("=" * 50)
    
    try:
        test_model_initialization()
        test_statistics()
        test_simulation_step()
        test_reset_functionality()
        
        print("\n" + "=" * 50)
        print("✓ 所有测试通过！模型功能正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
