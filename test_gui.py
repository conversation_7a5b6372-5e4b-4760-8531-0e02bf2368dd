"""
简单的PyQt5界面测试程序
"""
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PyQt5测试窗口")
        self.setGeometry(200, 200, 400, 300)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        
        # 添加标签
        label = QLabel("如果您能看到这个窗口，说明PyQt5工作正常！")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        # 添加按钮
        button = QPushButton("点击我")
        button.clicked.connect(self.on_button_click)
        layout.addWidget(button)
        
        central_widget.setLayout(layout)
        
        # 确保窗口显示
        self.show()
        self.raise_()
        self.activateWindow()
        
        print("测试窗口已创建并显示")
    
    def on_button_click(self):
        print("按钮被点击了！")


def main():
    print("启动PyQt5测试程序...")
    
    app = QApplication(sys.argv)
    
    print("创建测试窗口...")
    window = TestWindow()
    
    print("开始事件循环...")
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
