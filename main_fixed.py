"""
修复版本的Schelling隔离模型主程序
解决界面显示问题
"""
import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt


def main():
    """主函数"""
    print("正在启动Schelling隔离模型仿真...")
    
    # 设置环境变量解决显示问题
    os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
    os.environ['QT_SCALE_FACTOR'] = '1'
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setQuitOnLastWindowClosed(True)
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 设置应用程序信息
        app.setApplicationName("Schelling隔离模型仿真")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("Simulation Lab")
        
        print("导入GUI模块...")
        
        # 尝试导入GPU版本，如果失败则使用标准版本
        try:
            from gui_gpu import MainWindowGPU
            print("使用GPU加速版本界面")
            window = MainWindowGPU()
        except Exception as e:
            print(f"GPU版本加载失败: {e}")
            print("回退到标准版本...")
            from gui import MainWindow
            window = MainWindow()
        
        print("显示主窗口...")
        
        # 显示窗口
        window.show()
        window.raise_()
        window.activateWindow()
        
        # 确保窗口在屏幕中央
        screen = app.desktop().screenGeometry()
        window_geo = window.geometry()
        x = (screen.width() - window_geo.width()) // 2
        y = (screen.height() - window_geo.height()) // 2
        window.move(max(0, x), max(0, y))
        
        # 强制处理事件
        app.processEvents()
        
        print("程序界面应该已经显示")
        print("如果没有看到窗口，请检查：")
        print("1. 任务栏是否有程序图标")
        print("2. 按Alt+Tab切换窗口")
        print("3. 检查是否被其他窗口遮挡")
        print("4. 尝试最小化其他窗口")
        
        # 运行应用程序
        exit_code = app.exec_()
        print(f"程序退出，退出码: {exit_code}")
        sys.exit(exit_code)
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所需依赖：pip install PyQt5 numpy")
        input("按Enter键退出...")
        
    except Exception as e:
        print(f"程序启动时发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("按Enter键退出...")


if __name__ == "__main__":
    main()
