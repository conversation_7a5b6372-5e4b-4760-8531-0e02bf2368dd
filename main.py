"""
Schelling's Segregation Model - Main Application Entry Point
"""
import sys
from PyQt5.QtWidgets import QApplication
from gui import MainWindow


def main():
    """主函数"""
    # 启用高DPI支持
    QApplication.setAttribute(5, True)  # Qt.AA_EnableHighDpiScaling
    QApplication.setAttribute(6, True)  # Qt.AA_UseHighDpiPixmaps

    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("Schelling隔离模型仿真")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Simulation Lab")
    
    # 创建并显示主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
