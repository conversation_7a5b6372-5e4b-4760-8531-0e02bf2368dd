"""
PyQt5 GUI for Schelling's Segregation Model
"""
import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QGridLayout, QLabel, QSlider, 
                             QPushButton, QSpinBox, QDoubleSpinBox, QGroupBox,
                             QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QPainter, QColor, QFont
import numpy as np
from schelling_model import SchellingModel, AgentType


class GridWidget(QWidget):
    """网格显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.model = None
        self.cell_size = 8
        self.setMinimumSize(400, 400)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
    
    def set_model(self, model: SchellingModel):
        """设置要显示的模型"""
        self.model = model
        self.update_size()
        self.update()
    
    def update_size(self):
        """根据模型大小更新组件大小"""
        if self.model:
            width = self.model.width * self.cell_size
            height = self.model.height * self.cell_size
            self.setMinimumSize(width, height)
    
    def paintEvent(self, event):
        """绘制网格"""
        if not self.model:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 计算实际的单元格大小
        widget_width = self.width()
        widget_height = self.height()
        cell_width = widget_width / self.model.width
        cell_height = widget_height / self.model.height
        
        # 绘制网格
        for y in range(self.model.height):
            for x in range(self.model.width):
                cell = self.model.grid[y, x]
                
                # 确定颜色
                if cell == AgentType.EMPTY:
                    color = QColor(255, 255, 255)  # 白色
                elif hasattr(cell, 'type'):
                    if cell.type == AgentType.TYPE_A:
                        # 少数群体 - 蓝色，满意度影响透明度
                        if cell.satisfied:
                            color = QColor(0, 0, 255)  # 深蓝色
                        else:
                            color = QColor(100, 100, 255)  # 浅蓝色
                    else:  # TYPE_B
                        # 多数群体 - 红色，满意度影响透明度
                        if cell.satisfied:
                            color = QColor(255, 0, 0)  # 深红色
                        else:
                            color = QColor(255, 100, 100)  # 浅红色
                else:
                    color = QColor(128, 128, 128)  # 灰色
                
                # 绘制单元格
                rect_x = x * cell_width
                rect_y = y * cell_height
                painter.fillRect(int(rect_x), int(rect_y), 
                               int(cell_width), int(cell_height), color)
        
        # 绘制网格线（可选）
        if self.model.width <= 50 and self.model.height <= 50:
            painter.setPen(QColor(200, 200, 200))
            for x in range(self.model.width + 1):
                line_x = x * cell_width
                painter.drawLine(int(line_x), 0, int(line_x), widget_height)
            for y in range(self.model.height + 1):
                line_y = y * cell_height
                painter.drawLine(0, int(line_y), widget_width, int(line_y))


class ParameterPanel(QGroupBox):
    """参数配置面板"""
    
    parameters_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__("参数配置", parent)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QGridLayout()
        
        # 网格大小
        layout.addWidget(QLabel("网格宽度:"), 0, 0)
        self.width_spinbox = QSpinBox()
        self.width_spinbox.setRange(10, 100)
        self.width_spinbox.setValue(50)
        self.width_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.width_spinbox, 0, 1)
        
        layout.addWidget(QLabel("网格高度:"), 1, 0)
        self.height_spinbox = QSpinBox()
        self.height_spinbox.setRange(10, 100)
        self.height_spinbox.setValue(50)
        self.height_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.height_spinbox, 1, 1)
        
        # 人口密度
        layout.addWidget(QLabel("人口密度:"), 2, 0)
        self.density_spinbox = QDoubleSpinBox()
        self.density_spinbox.setRange(0.1, 1.0)
        self.density_spinbox.setSingleStep(0.1)
        self.density_spinbox.setValue(0.8)
        self.density_spinbox.setDecimals(2)
        self.density_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.density_spinbox, 2, 1)
        
        # 少数群体比例
        layout.addWidget(QLabel("少数群体比例:"), 3, 0)
        self.minority_spinbox = QDoubleSpinBox()
        self.minority_spinbox.setRange(0.1, 0.9)
        self.minority_spinbox.setSingleStep(0.1)
        self.minority_spinbox.setValue(0.3)
        self.minority_spinbox.setDecimals(2)
        self.minority_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.minority_spinbox, 3, 1)
        
        # 相似性阈值
        layout.addWidget(QLabel("相似性阈值:"), 4, 0)
        self.homophily_spinbox = QDoubleSpinBox()
        self.homophily_spinbox.setRange(0.0, 1.0)
        self.homophily_spinbox.setSingleStep(0.1)
        self.homophily_spinbox.setValue(0.3)
        self.homophily_spinbox.setDecimals(2)
        self.homophily_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.homophily_spinbox, 4, 1)
        
        self.setLayout(layout)
    
    def get_parameters(self):
        """获取当前参数"""
        return {
            'width': self.width_spinbox.value(),
            'height': self.height_spinbox.value(),
            'density': self.density_spinbox.value(),
            'minority_pc': self.minority_spinbox.value(),
            'homophily': self.homophily_spinbox.value()
        }


class StatisticsPanel(QGroupBox):
    """统计信息面板"""

    def __init__(self, parent=None):
        super().__init__("统计信息", parent)
        self.setup_ui()

    def setup_ui(self):
        layout = QGridLayout()

        # 基本统计信息
        self.iteration_label = QLabel("迭代次数: 0")
        self.satisfaction_label = QLabel("总体满意度: 0.00%")
        self.segregation_label = QLabel("隔离指数: 0.00")
        self.agents_label = QLabel("代理总数: 0")

        layout.addWidget(self.iteration_label, 0, 0, 1, 2)
        layout.addWidget(self.satisfaction_label, 1, 0, 1, 2)
        layout.addWidget(self.segregation_label, 2, 0, 1, 2)
        layout.addWidget(self.agents_label, 3, 0, 1, 2)

        # 分组统计
        layout.addWidget(QLabel("群体统计:"), 4, 0, 1, 2)

        self.type_a_count_label = QLabel("少数群体: 0")
        self.type_a_satisfaction_label = QLabel("满意度: 0.00%")
        self.type_b_count_label = QLabel("多数群体: 0")
        self.type_b_satisfaction_label = QLabel("满意度: 0.00%")

        layout.addWidget(self.type_a_count_label, 5, 0)
        layout.addWidget(self.type_a_satisfaction_label, 5, 1)
        layout.addWidget(self.type_b_count_label, 6, 0)
        layout.addWidget(self.type_b_satisfaction_label, 6, 1)

        # 图例
        legend_layout = QHBoxLayout()

        # 少数群体图例
        minority_satisfied_frame = QFrame()
        minority_satisfied_frame.setFixedSize(15, 15)
        minority_satisfied_frame.setStyleSheet("background-color: blue; border: 1px solid black;")

        minority_unsatisfied_frame = QFrame()
        minority_unsatisfied_frame.setFixedSize(15, 15)
        minority_unsatisfied_frame.setStyleSheet("background-color: lightblue; border: 1px solid black;")

        legend_layout.addWidget(QLabel("少数群体:"))
        legend_layout.addWidget(minority_satisfied_frame)
        legend_layout.addWidget(QLabel("满意"))
        legend_layout.addWidget(minority_unsatisfied_frame)
        legend_layout.addWidget(QLabel("不满意"))

        legend_layout2 = QHBoxLayout()

        # 多数群体图例
        majority_satisfied_frame = QFrame()
        majority_satisfied_frame.setFixedSize(15, 15)
        majority_satisfied_frame.setStyleSheet("background-color: red; border: 1px solid black;")

        majority_unsatisfied_frame = QFrame()
        majority_unsatisfied_frame.setFixedSize(15, 15)
        majority_unsatisfied_frame.setStyleSheet("background-color: lightcoral; border: 1px solid black;")

        legend_layout2.addWidget(QLabel("多数群体:"))
        legend_layout2.addWidget(majority_satisfied_frame)
        legend_layout2.addWidget(QLabel("满意"))
        legend_layout2.addWidget(majority_unsatisfied_frame)
        legend_layout2.addWidget(QLabel("不满意"))

        legend_layout.addStretch()
        legend_layout2.addStretch()

        layout.addLayout(legend_layout, 7, 0, 1, 2)
        layout.addLayout(legend_layout2, 8, 0, 1, 2)

        self.setLayout(layout)

    def update_statistics(self, stats):
        """更新统计信息显示"""
        self.iteration_label.setText(f"迭代次数: {stats['iteration']}")
        self.satisfaction_label.setText(f"总体满意度: {stats['satisfaction_rate']:.2%}")
        self.segregation_label.setText(f"隔离指数: {stats['segregation_index']:.3f}")
        self.agents_label.setText(f"代理总数: {stats['total_agents']}")

        self.type_a_count_label.setText(f"少数群体: {stats['type_a_count']}")
        self.type_a_satisfaction_label.setText(f"满意度: {stats['type_a_satisfaction']:.2%}")
        self.type_b_count_label.setText(f"多数群体: {stats['type_b_count']}")
        self.type_b_satisfaction_label.setText(f"满意度: {stats['type_b_satisfaction']:.2%}")


class ControlPanel(QGroupBox):
    """控制面板"""

    start_simulation = pyqtSignal()
    pause_simulation = pyqtSignal()
    reset_simulation = pyqtSignal()
    speed_changed = pyqtSignal(int)

    def __init__(self, parent=None):
        super().__init__("仿真控制", parent)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("开始")
        self.start_button.clicked.connect(self.start_simulation.emit)
        button_layout.addWidget(self.start_button)

        self.pause_button = QPushButton("暂停")
        self.pause_button.clicked.connect(self.pause_simulation.emit)
        self.pause_button.setEnabled(False)
        button_layout.addWidget(self.pause_button)

        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.reset_simulation.emit)
        button_layout.addWidget(self.reset_button)

        layout.addLayout(button_layout)

        # 速度控制
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("仿真速度:"))

        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(1, 10)
        self.speed_slider.setValue(5)
        self.speed_slider.valueChanged.connect(self.speed_changed.emit)
        speed_layout.addWidget(self.speed_slider)

        self.speed_label = QLabel("5")
        self.speed_slider.valueChanged.connect(lambda v: self.speed_label.setText(str(v)))
        speed_layout.addWidget(self.speed_label)

        layout.addLayout(speed_layout)

        self.setLayout(layout)

    def set_simulation_running(self, running):
        """设置仿真运行状态"""
        self.start_button.setEnabled(not running)
        self.pause_button.setEnabled(running)


class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.model = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_simulation)
        self.simulation_running = False

        self.setup_ui()
        self.create_model()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("Schelling隔离模型仿真")
        self.setGeometry(100, 100, 1200, 800)

        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)

        # 左侧面板
        left_panel = QVBoxLayout()

        # 参数面板
        self.parameter_panel = ParameterPanel()
        self.parameter_panel.parameters_changed.connect(self.on_parameters_changed)
        left_panel.addWidget(self.parameter_panel)

        # 控制面板
        self.control_panel = ControlPanel()
        self.control_panel.start_simulation.connect(self.start_simulation)
        self.control_panel.pause_simulation.connect(self.pause_simulation)
        self.control_panel.reset_simulation.connect(self.reset_simulation)
        self.control_panel.speed_changed.connect(self.set_simulation_speed)
        left_panel.addWidget(self.control_panel)

        # 统计面板
        self.statistics_panel = StatisticsPanel()
        left_panel.addWidget(self.statistics_panel)

        left_panel.addStretch()

        # 右侧网格显示
        self.grid_widget = GridWidget()

        # 添加到主布局
        left_widget = QWidget()
        left_widget.setLayout(left_panel)
        left_widget.setFixedWidth(300)

        main_layout.addWidget(left_widget)
        main_layout.addWidget(self.grid_widget, 1)

    def create_model(self):
        """创建模型"""
        params = self.parameter_panel.get_parameters()
        self.model = SchellingModel(**params)
        self.grid_widget.set_model(self.model)
        self.update_statistics()

    def on_parameters_changed(self):
        """参数改变时的处理"""
        if not self.simulation_running:
            self.create_model()

    def start_simulation(self):
        """开始仿真"""
        self.simulation_running = True
        self.control_panel.set_simulation_running(True)
        self.timer.start(200)  # 默认200ms间隔

    def pause_simulation(self):
        """暂停仿真"""
        self.simulation_running = False
        self.control_panel.set_simulation_running(False)
        self.timer.stop()

    def reset_simulation(self):
        """重置仿真"""
        self.pause_simulation()
        self.model.reset()
        self.grid_widget.update()
        self.update_statistics()

    def set_simulation_speed(self, speed):
        """设置仿真速度"""
        # 速度1-10，对应1000ms-100ms的间隔
        interval = 1100 - speed * 100
        if self.timer.isActive():
            self.timer.setInterval(interval)

    def update_simulation(self):
        """更新仿真"""
        if self.model.step():
            self.grid_widget.update()
            self.update_statistics()
        else:
            # 仿真结束（所有代理都满意或无法移动）
            self.pause_simulation()

    def update_statistics(self):
        """更新统计信息"""
        stats = self.model.get_statistics()
        self.statistics_panel.update_statistics(stats)
